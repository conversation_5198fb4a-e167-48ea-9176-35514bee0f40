# 团购详情页行业优化需求分析（基于当前仓库代码逻辑）

## 📋 文档说明

本文档基于原始需求和当前仓库代码逻辑，重点澄清**业务概念边界**、**技术实现现状**和**需求复杂度评估**，旨在指导更准确的技术方案设计。

## 1. 🆕 业务概念澄清和边界定义

### 1.1 核心概念澄清

**关键概念映射**：
| 需求术语 | 实际业务概念 | 技术实现 | 说明 |
|----------|-------------|----------|------|
| **翻单引导标签** | 商户团单升级提醒机制 | 基于商品分类和属性完整性的标签展示逻辑 | 通过ProductCategoryFetcher获取分类，结合ProductAttrConfigService判断属性完整性 |
| **待升级标签** | 团单必填属性未完整填写状态 | 基于ProductAttrConfigService的属性配置检查 | 涉及眼镜、眼科、口腔齿科等二级类目的必填属性验证 |
| **待更新标签** | 旧团单类目向新类目迁移提醒 | 基于商品分类映射的类目升级逻辑 | 通过类目映射表判断是否为旧类目，触发B端清洗流程 |
| **质保信息单位** | 质保期限的时间单位扩展 | DealDetailStructuredDetailVO中的质保字段扩展 | 支持"年"与"天"单位，需兼容现有数据结构 |
| **取镜时间类型** | 配镜服务的时间策略分类 | AvailableTimeStrategyEnum的扩展 | 新增多种取镜时间类型的枚举值 |
| **标题拼接模式** | 团单标题的自动生成策略 | 标题构建逻辑的调整 | 从模式3调整为模式2，涉及前端展示逻辑 |

### 1.2 业务关系澄清

- **行业分类体系**：眼镜行业（近视配镜、儿童配镜、老花眼镜等）、眼科行业（儿童普通眼镜、医学配镜等）、口腔行业（补牙、儿童补牙等）
- **属性依赖关系**：镜片技术、度数/折射率、验光操作人员等属性间的关联性
- **数据流转关系**：团购详情→翻单映射→属性透传的数据链路

## 2. 现有技术实现现状分析

### 2.1 现有代码基础

**已有实现**：
- **模块化架构**：基于DDD架构的产品详情页通用模块服务
- **核心服务**：ProductDetailPageCommonModuleSpiImpl作为主要RPC服务入口
- **数据获取器**：ProductBaseInfoFetcher、ProductCategoryFetcher等获取商品和分类信息
- **模块构建器**：ProductStructuredDetailModuleFactory用于构建结构化详情模块
- **配置服务**：ProductAttrConfigService管理商品属性配置

### 2.2 数据链路现状

**现有数据流**：
```
请求 → ProductDetailPageCommonModuleSpiImpl → ModuleArrangeFrameworkRunner → 
各Builder组件 → Fetcher获取数据 → 外部RPC服务/缓存 → 响应构建
```

**已有数据字段**：
- CommonDataVO：包含商品类型、分类ID、商户信息等基础数据
- DealDetailStructuredDetailVO：结构化团详数据，支持图标、内容、跳转链接等
- ModuleKeyConstants：定义了29个模块键，包括STRUCTURED_DEAL_DETAILS等

**已有数据来源的接口**：
- QueryCenterAclService：查询中心访问控制服务
- CompositeAtomService：集成20+个外部RPC服务
- ProductAttrConfigService：商品属性配置服务

### 2.3 需求复杂度准确评估

**低复杂度**（配置调整类）：
- 质保信息单位属性新增
- 服务时长范围逻辑调整
- 字体样式调整

**中复杂度**（业务逻辑扩展）：
- 翻单引导标签展示逻辑
- 取镜时间类型新增
- 镜片技术字段多选支持
- 验光操作人员多选调整

**高复杂度**（数据结构变更）：
- 标题拼接逻辑更新（涉及前后端协调）
- 团详数据结构优化与翻单映射规则
- 补牙科普信息的条件展示逻辑

## 3. 业务范围

**涉及行业**：
- 眼镜行业：近视配镜、儿童配镜、老花眼镜、仅镜框、仅镜片、太阳眼镜、隐形眼镜
- 眼科行业：儿童普通眼镜、医学配镜、离焦镜、OK镜、离焦软镜、RGP镜、其他接触镜
- 口腔行业：补牙、儿童补牙

**涉及平台**：
- C端：团购详情页展示优化
- B端：开店宝后台标签引导、编辑流程优化

## 4. 需求内容

### 4.1 核心需求澄清

- **主要目标**：优化团购详情页的行业专业性展示，提升商户翻单转化率
- **实现方式**：基于现有模块化架构，扩展属性配置和展示逻辑
- **平台支持**：美团App、点评App、小程序、开店宝后台
- **实验支持**：需要支持灰度发布和A/B测试

### 4.2 实际实现的功能点

**功能点1：翻单引导标签系统**
- 基于ProductAttrConfigService扩展属性完整性检查
- 新增标签展示逻辑和hover浮层
- 集成B端编辑流程的类目选择引导

**功能点2：行业专业信息展示优化**
- 质保信息支持多单位展示
- 取镜时间类型多样化
- 度数/折射率关系说明入口
- 补牙材料科普信息展示

**功能点3：属性字段功能增强**
- 镜片技术字段多选支持
- 验光操作人员多选调整
- 套餐包含属性新增

**功能点4：数据结构优化**
- 团详信息翻单透传机制
- 标题拼接逻辑调整
- 服务时长数据清洗

## 5. 需求描述中存在的问题

### 5.1 需求描述不清/边界模糊点
- **标签识别规则表格缺失**：需求中提到"详见表格"但未提供具体的类目ID和规则映射
- **弹窗引导具体交互**：B端类目选择弹窗的具体交互流程和UI设计未明确
- **数据清洗策略**：服务时长数据清洗的具体规则和影响范围需要明确

### 5.2 与现有业务/系统冲突点
- **模块键冲突风险**：新增功能可能与现有ModuleKeyConstants中的模块键产生冲突
- **属性配置兼容性**：ProductAttrConfigService的配置变更可能影响现有业务逻辑
- **数据结构向后兼容**：DealDetailStructuredDetailVO的字段扩展需要保证向后兼容

### 5.3 其他需补充或澄清的问题
- **性能影响评估**：多选字段和复杂展示逻辑对页面加载性能的影响
- **缓存策略调整**：新增属性和配置的缓存策略需要重新设计
- **监控和告警**：新功能的监控指标和异常告警机制
- **灰度发布策略**：不同行业和类目的分阶段发布计划
- **数据迁移方案**：现有数据向新数据结构的迁移策略和回滚方案
