# 团购详情页商品属性需求分析（基于当前仓库代码逻辑）

## 📋 文档说明

本文档基于原始需求和当前仓库代码逻辑，重点澄清**业务概念边界**、**技术实现现状**和**需求复杂度评估**，旨在指导更准确的技术方案设计。

## 1. 🆕 业务概念澄清和边界定义

### 1.1 核心概念澄清

**关键概念映射**：
| 需求术语 | 实际业务概念 | 技术实现 | 说明 |
|----------|-------------|----------|------|
| **团购详情页（团详页）** | 结构化团详模块 | `ModuleDetailStructuredDetailVO<T>` | 当前已有泛型化的结构化团详模块实现 |
| **商品属性** | 团购商品属性 | `ProductAttr` + `ProductAttrFetcher` | 基于查询中心的商品属性获取机制 |
| **团单分类** | 商品分类信息 | `ProductCategory` + `ProductCategoryFetcher` | 一、二、三级分类体系 |
| **图文详情** | 图文详情模块 | `ImageTextDetailVO` | 独立的图文详情模块，支持文本、图片、视频内容 |
| **闪购侧属性** | 外部系统商品属性 | 查询中心聚合数据 | 通过`QueryCenterAggregateFetcher`获取 |

### 1.2 业务关系澄清

- **团详页与图文详情关系**：团详页位于图文详情字段上方，两者为独立模块
- **属性优先级机制**：分类 > 品牌 > 规格 > 商品品类 > 保质期 > 生产日期 > 规格 > 口味 > 包装
- **属性配置机制**：基于`ProductTypeEnum`和二级分类ID的配置体系

## 2. 现有技术实现现状分析

### 2.1 现有代码基础

**已有实现**：
- ✅ **结构化团详模块框架**：`ModuleDetailStructuredDetailVO<T>`泛型设计
- ✅ **商品属性获取机制**：`ProductAttrFetcher` + `ProductAttrConfigService`
- ✅ **商品分类获取机制**：`ProductCategoryFetcher` + `ProductCategory`
- ✅ **图文详情模块**：`ImageTextDetailVO` + `ImageTextDetailContent`
- ✅ **模块编排框架**：基于`@Builder`注解的模块构建体系
- ✅ **属性配置服务**：Lion配置中心动态配置属性字段

### 2.2 数据链路现状

**现有数据流**：
```
请求 → ProductAttrFetcher → QueryCenterAggregateFetcher → 查询中心 → AttrDTO → ProductAttr
```

**已有数据字段**：
- `AttrDTO`: 属性名称、属性值列表
- `ProductCategory`: 一级、二级、三级分类ID
- `DealDetailStructuredDetailVO`: 团详行数据结构

**已有数据来源的接口**：
- 查询中心Thrift接口：`QueryByDealGroupIdRequestBuilder`
- 属性配置：Lion配置中心动态配置
- 分类信息：网关层传递的分类参数

### 2.3 需求复杂度准确评估

**低复杂度**（现有机制支持）：
- 商品属性字段获取和展示
- 团单分类信息展示
- 属性优先级排序逻辑

**中等复杂度**（需要扩展现有实现）：
- 团详页新增字段的UI展示逻辑
- 最多展示5个属性的过滤机制
- 空值字段不下发的逻辑

**高复杂度**（需要新增实现）：
- 闪购侧属性与综侧属性的匹配逻辑
- 图文详情自动填充机制（头图+文字详情）

## 3. 业务范围

**核心业务范围**：
- 团购详情页商品属性字段展示
- 团单分类信息展示
- 属性优先级排序和数量限制
- 图文详情内容自动填充

**边界范围**：
- 仅涉及团购详情页，不包含货架部分
- 仅处理展示逻辑，不涉及属性数据的创建和编辑
- 支持美团App和点评App预览

## 4. 需求内容

### 4.1 核心需求澄清

- **主要目标**：在团购详情页增加"团单分类"和"商品属性"字段展示
- **实现方式**：基于现有结构化团详模块框架扩展
- **平台支持**：美团App + 点评App
- **实验支持**：需要支持预览功能（扫码查看）

### 4.2 实际实现的功能点

**功能点1：团详页属性字段展示**
- 在团单属性模块增加"团单分类"和"商品属性"字段
- 按照优先级顺序展示：分类>品牌>规格>商品品类>保质期>生产日期>规格>口味>包装
- 最多展示5个属性，空值不下发

**功能点2：商品结构创建适配**
- 闪购侧关键属性与普通属性放置在团购详情模块
- 位置在图文详情字段上方
- 品牌字段设为必填，其他字段非必填且可隐藏

**功能点3：图文详情自动填充**
- 团购头图自动填充到图文详情的图片位置
- 闪购侧"文字详情"填充到图文详情的文字部分
- 文字详情无内容时不填充文字部分

**功能点4：批量上单支持**
- 支持团详内容编辑和预览
- 预览功能支持美团App和点评App扫码查看

## 5. 需求描述中存在的问题

### 5.1 需求描述不清/边界模糊点
1. **属性匹配规则不明确**：需求中提到"与运营筛选出的二级类目下共有的核心字段做匹配"，但未明确匹配的具体规则和数据来源
2. **属性优先级重复**：优先级顺序中"规格"出现两次，需要澄清具体的属性字段名称
3. **图文详情填充时机**：未明确是在商品创建时填充还是展示时动态填充

### 5.2 与现有业务/系统冲突点
1. **模块键冲突风险**：当前`STRUCTURED_DEAL_DETAILS`模块键已被多个Builder使用，需要确认是否复用
2. **属性配置冲突**：现有属性配置基于Lion配置中心，新增属性可能与现有配置冲突
3. **数据一致性问题**：闪购侧数据与综侧数据的同步时机和一致性保障机制

### 5.3 其他需补充或澄清的问题
1. **性能影响评估**：新增属性字段对查询中心调用和响应时间的影响
2. **降级策略**：属性获取失败时的降级展示策略
3. **AB实验支持**：是否需要支持属性展示的AB实验配置
4. **监控和告警**：新增功能的监控指标和异常告警机制
5. **数据埋点**：新增字段的用户行为埋点需求
